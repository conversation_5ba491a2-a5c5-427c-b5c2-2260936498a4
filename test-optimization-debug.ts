/**
 * Debug script to test optimization for project JzUb0Ux6gspkBKnzzfC-v
 * This script will help identify why the UI reports all parts cannot be cut
 * when the visualization shows some pieces are actually cut.
 */

import { OptimizationEngine } from './src/lib/optimization/server-engine';

// Mock project data based on the test scenario
const testProjectId = 'JzUb0Ux6gspkBKnzzfC-v';

// Test materials (small sheets)
const testMaterials = [
  {
    id: 'material-1',
    name: 'Plywood Sheet 1',
    length: 600,
    width: 400,
    thickness: 18,
    unit: 'MM' as const,
    quantity: 2,
    cost: 50
  }
];

// Test pieces (some should fit, some shouldn't)
const testPieces = [
  {
    id: 'piece-1',
    name: 'Small Piece 1',
    length: 200,
    width: 150,
    thickness: 18,
    unit: 'MM' as const,
    quantity: 2,
    grainDirection: 'NONE' as const,
    priority: 1
  },
  {
    id: 'piece-2',
    name: 'Small Piece 2',
    length: 300,
    width: 200,
    thickness: 18,
    unit: 'MM' as const,
    quantity: 1,
    grainDirection: 'NONE' as const,
    priority: 1
  },
  {
    id: 'piece-3',
    name: 'Large Piece (Too Big)',
    length: 800,
    width: 500,
    thickness: 18,
    unit: 'MM' as const,
    quantity: 1,
    grainDirection: 'NONE' as const,
    priority: 1
  },
  {
    id: 'piece-4',
    name: 'Another Large Piece (Too Big)',
    length: 700,
    width: 600,
    thickness: 18,
    unit: 'MM' as const,
    quantity: 2,
    grainDirection: 'NONE' as const,
    priority: 1
  }
];

async function debugOptimization() {
  console.log('🔍 Debugging Optimization for Project:', testProjectId);
  console.log('📏 Materials:', testMaterials.length);
  console.log('🧩 Pieces:', testPieces.length);
  console.log('');

  // Calculate expected results
  const totalPieces = testPieces.reduce((sum, piece) => sum + piece.quantity, 0);
  console.log('📊 Expected Results:');
  console.log(`   Total pieces to cut: ${totalPieces}`);
  console.log('   Expected to fit: Small Piece 1 (qty: 2), Small Piece 2 (qty: 1) = 3 pieces');
  console.log('   Expected NOT to fit: Large Piece (qty: 1), Another Large Piece (qty: 2) = 3 pieces');
  console.log('');

  try {
    const engine = new OptimizationEngine();
    
    const optimizationRequest = {
      materials: testMaterials,
      pieces: testPieces,
      sawKerf: 3.0,
      kerfUnit: 'MM' as const,
      projectId: testProjectId,
      userId: 'test-user'
    };

    console.log('🚀 Running optimization...');
    const result = await engine.optimize(optimizationRequest);

    console.log('✅ Optimization completed!');
    console.log('');

    // Debug: Check internal state
    console.log('🔧 DEBUG INFO:');
    console.log('==============');
    if (result.layouts) {
      const totalPlacedPieces = result.layouts.reduce((sum, layout) => sum + layout.pieces.length, 0);
      console.log(`Total pieces placed in layouts: ${totalPlacedPieces}`);

      // Check for duplicates
      const allPlacedPieceIds = result.layouts.flatMap(layout => layout.pieces.map(p => p.id));
      const uniquePieceIds = new Set(allPlacedPieceIds);
      console.log(`Unique piece IDs placed: ${uniquePieceIds.size}`);
      console.log(`Total piece placements: ${allPlacedPieceIds.length}`);

      if (uniquePieceIds.size !== allPlacedPieceIds.length) {
        console.log('⚠️  WARNING: Duplicate pieces detected in layouts!');
        const duplicates = allPlacedPieceIds.filter((id, index) => allPlacedPieceIds.indexOf(id) !== index);
        console.log(`Duplicate IDs: ${[...new Set(duplicates)].join(', ')}`);
      }
    }
    console.log('');

    // Analyze results
    console.log('📋 RESULTS ANALYSIS:');
    console.log('===================');
    console.log(`Success: ${result.success}`);
    console.log(`Layouts generated: ${result.layouts?.length || 0}`);
    console.log(`Uncut pieces: ${result.uncutPieces?.length || 0}`);
    if (result.error) {
      console.log(`❌ Error: ${result.error}`);
    }
    console.log('');

    if (result.metadata) {
      console.log('📈 Metadata:');
      console.log(`   Total pieces: ${result.metadata.totalPieces}`);
      console.log(`   Cut pieces: ${result.metadata.cutPieces}`);
      console.log(`   Uncut pieces: ${result.metadata.uncutPieces}`);
      console.log(`   Efficiency: ${result.metadata.efficiency?.toFixed(1)}%`);
      console.log('');
    }

    // Detailed layout analysis
    if (result.layouts && result.layouts.length > 0) {
      console.log('🗂️  LAYOUT DETAILS:');
      console.log('==================');
      result.layouts.forEach((layout, index) => {
        console.log(`Sheet ${index + 1}: ${layout.baseMaterial.name}`);
        console.log(`   Pieces placed: ${layout.pieces.length}`);
        console.log(`   Efficiency: ${layout.efficiency.toFixed(1)}%`);
        console.log(`   Pieces:`);
        layout.pieces.forEach((piece, pieceIndex) => {
          console.log(`     ${pieceIndex + 1}. ${piece.name} (${piece.length}×${piece.width})`);
        });
        console.log('');
      });
    }

    // Detailed uncut pieces analysis
    if (result.uncutPieces && result.uncutPieces.length > 0) {
      console.log('❌ UNCUT PIECES DETAILS:');
      console.log('========================');
      result.uncutPieces.forEach((piece, index) => {
        console.log(`${index + 1}. ${piece.name}`);
        console.log(`   Dimensions: ${piece.length}×${piece.width} ${piece.unit}`);
        console.log(`   Quantity not cut: ${piece.quantity}`);
        console.log('');
      });
    }

    // Check for the specific issue
    console.log('🔍 ISSUE ANALYSIS:');
    console.log('==================');
    
    const actualCutPieces = result.metadata?.cutPieces || 0;
    const actualUncutPieces = result.metadata?.uncutPieces || 0;
    const expectedCutPieces = 3; // Small pieces should fit
    const expectedUncutPieces = 3; // Large pieces shouldn't fit

    console.log(`Expected cut pieces: ${expectedCutPieces}, Actual: ${actualCutPieces}`);
    console.log(`Expected uncut pieces: ${expectedUncutPieces}, Actual: ${actualUncutPieces}`);

    if (actualCutPieces > 0 && actualUncutPieces > 0) {
      console.log('✅ CORRECT: Some pieces cut, some uncut (as expected)');
    } else if (actualCutPieces === 0) {
      console.log('❌ ISSUE: No pieces cut (UI might show "all parts have not been able to be cut")');
    } else if (actualUncutPieces === 0) {
      console.log('❌ ISSUE: All pieces cut (unexpected given material constraints)');
    }

    // Check UI condition logic
    console.log('');
    console.log('🖥️  UI CONDITION CHECK:');
    console.log('======================');
    
    const hasLayouts = result.layouts && result.layouts.length > 0;
    const hasUncutPieces = result.uncutPieces && result.uncutPieces.length > 0;
    const allPiecesUncut = actualCutPieces === 0;
    
    console.log(`Has layouts: ${hasLayouts}`);
    console.log(`Has uncut pieces: ${hasUncutPieces}`);
    console.log(`All pieces uncut: ${allPiecesUncut}`);
    
    if (hasLayouts && hasUncutPieces && !allPiecesUncut) {
      console.log('✅ UI should show: "X piece(s) could not be cut" (partial success)');
    } else if (hasLayouts && !hasUncutPieces) {
      console.log('✅ UI should show: Success message (all pieces cut)');
    } else if (!hasLayouts || allPiecesUncut) {
      console.log('❌ UI might show: "All parts have not been able to be cut"');
    }

    return result;

  } catch (error) {
    console.error('❌ Optimization failed:', error);
    console.error('Error details:', error instanceof Error ? error.message : 'Unknown error');
    return null;
  }
}

// Run the debug script
if (require.main === module) {
  debugOptimization()
    .then((result) => {
      if (result) {
        console.log('');
        console.log('🎯 Debug completed successfully!');
        process.exit(0);
      } else {
        console.log('');
        console.log('💥 Debug failed!');
        process.exit(1);
      }
    })
    .catch((error) => {
      console.error('💥 Unexpected error:', error);
      process.exit(1);
    });
}

export { debugOptimization, testMaterials, testPieces };
