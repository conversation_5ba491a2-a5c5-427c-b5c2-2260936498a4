# TODO List

Pipeline of ideas for the app

## Must have

- free accounts should only be allowed to create 2 projects
- free accounts should only be allowed to call optimization APIs 5 times per day and 10 times per month
- I want to be able to track if a customer registered with two different accounts by using the ip address of the user. This should be done on the backend and the user should get a warning if they register with the same ip address as another account. The registration at that time should not be allowed.
- user profile page
- user settings page
- add payment processing
- add subscription processing   

## Nice to have

- internationalization (i18n)