import { pgTable, text, timestamp, unique, boolean, foreignKey, real, integer, json, pgEnum } from "drizzle-orm/pg-core"
import { sql } from "drizzle-orm"

export const grainDirection = pgEnum("GrainDirection", ['NONE', 'HORIZONTAL', 'VERTICAL', 'EITHER'])
export const unit = pgEnum("Unit", ['MM', 'CM', 'M', 'IN', 'FT'])
export const userPlan = pgEnum("UserPlan", ['FREE', 'PRO', 'ENTERPRISE'])


export const verification = pgTable("verification", {
	id: text().primaryKey().notNull(),
	identifier: text().notNull(),
	value: text().notNull(),
	expiresAt: timestamp({ mode: 'string' }).notNull(),
	createdAt: timestamp({ mode: 'string' }),
	updatedAt: timestamp({ mode: 'string' }),
});

export const user = pgTable("user", {
	id: text().primaryKey().notNull(),
	name: text().notNull(),
	email: text().notNull(),
	emailVerified: boolean().notNull(),
	image: text(),
	createdAt: timestamp({ mode: 'string' }).notNull(),
	updatedAt: timestamp({ mode: 'string' }).notNull(),
}, (table) => [
	unique("user_email_unique").on(table.email),
]);

export const session = pgTable("session", {
	id: text().primaryKey().notNull(),
	expiresAt: timestamp({ mode: 'string' }).notNull(),
	token: text().notNull(),
	createdAt: timestamp({ mode: 'string' }).notNull(),
	updatedAt: timestamp({ mode: 'string' }).notNull(),
	ipAddress: text(),
	userAgent: text(),
	userId: text().notNull(),
}, (table) => [
	foreignKey({
			columns: [table.userId],
			foreignColumns: [user.id],
			name: "session_userId_user_id_fk"
		}),
	unique("session_token_unique").on(table.token),
]);

export const account = pgTable("account", {
	id: text().primaryKey().notNull(),
	accountId: text().notNull(),
	providerId: text().notNull(),
	userId: text().notNull(),
	accessToken: text(),
	refreshToken: text(),
	idToken: text(),
	accessTokenExpiresAt: timestamp({ mode: 'string' }),
	refreshTokenExpiresAt: timestamp({ mode: 'string' }),
	scope: text(),
	password: text(),
	createdAt: timestamp({ mode: 'string' }).notNull(),
	updatedAt: timestamp({ mode: 'string' }).notNull(),
}, (table) => [
	foreignKey({
			columns: [table.userId],
			foreignColumns: [user.id],
			name: "account_userId_user_id_fk"
		}),
]);

export const projects = pgTable("projects", {
	id: text().primaryKey().notNull(),
	name: text().notNull(),
	description: text(),
	sawKerf: real().default(3).notNull(),
	kerfUnit: unit().default('MM').notNull(),
	createdAt: timestamp({ mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp({ mode: 'string' }).defaultNow().notNull(),
	userId: text().notNull(),
}, (table) => [
	foreignKey({
			columns: [table.userId],
			foreignColumns: [user.id],
			name: "projects_userId_user_id_fk"
		}).onDelete("cascade"),
]);

export const pieces = pgTable("pieces", {
	id: text().primaryKey().notNull(),
	name: text().notNull(),
	length: real().notNull(),
	width: real().notNull(),
	thickness: real(),
	unit: unit().default('MM').notNull(),
	quantity: integer().default(1).notNull(),
	grainDirection: grainDirection().default('NONE').notNull(),
	priority: integer().default(1).notNull(),
	notes: text(),
	createdAt: timestamp({ mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp({ mode: 'string' }).defaultNow().notNull(),
	projectId: text().notNull(),
}, (table) => [
	foreignKey({
			columns: [table.projectId],
			foreignColumns: [projects.id],
			name: "pieces_projectId_projects_id_fk"
		}).onDelete("cascade"),
]);

export const optimizationResults = pgTable("optimization_results", {
	id: text().primaryKey().notNull(),
	algorithm: text().notNull(),
	efficiency: real().notNull(),
	wastePercentage: real().notNull(),
	totalSheets: integer().notNull(),
	totalCost: real(),
	processingTime: integer().notNull(),
	metadata: json(),
	createdAt: timestamp({ mode: 'string' }).defaultNow().notNull(),
	projectId: text().notNull(),
	userId: text().notNull(),
}, (table) => [
	foreignKey({
			columns: [table.projectId],
			foreignColumns: [projects.id],
			name: "optimization_results_projectId_projects_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [user.id],
			name: "optimization_results_userId_user_id_fk"
		}).onDelete("cascade"),
]);

export const optimizationMaterials = pgTable("optimization_materials", {
	id: text().primaryKey().notNull(),
	quantity: integer().notNull(),
	materialSnapshot: json().notNull(),
	optimizationResultId: text().notNull(),
	materialId: text().notNull(),
}, (table) => [
	foreignKey({
			columns: [table.materialId],
			foreignColumns: [materials.id],
			name: "optimization_materials_materialId_materials_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.optimizationResultId],
			foreignColumns: [optimizationResults.id],
			name: "optimization_materials_optimizationResultId_optimization_result"
		}).onDelete("cascade"),
]);

export const sheets = pgTable("sheets", {
	id: text().primaryKey().notNull(),
	sheetIndex: integer().notNull(),
	widthUsed: real().notNull(),
	heightUsed: real().notNull(),
	materialSnapshot: json().notNull(),
	optimizationResultId: text().notNull(),
}, (table) => [
	foreignKey({
			columns: [table.optimizationResultId],
			foreignColumns: [optimizationResults.id],
			name: "sheets_optimizationResultId_optimization_results_id_fk"
		}).onDelete("cascade"),
]);

export const placedPieces = pgTable("placed_pieces", {
	id: text().primaryKey().notNull(),
	x: real().notNull(),
	y: real().notNull(),
	packedWidth: real().notNull(),
	packedHeight: real().notNull(),
	rotation: real().default(0).notNull(),
	color: text(),
	pieceSnapshot: json().notNull(),
	sheetId: text().notNull(),
	pieceId: text().notNull(),
}, (table) => [
	foreignKey({
			columns: [table.sheetId],
			foreignColumns: [sheets.id],
			name: "placed_pieces_sheetId_sheets_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.pieceId],
			foreignColumns: [pieces.id],
			name: "placed_pieces_pieceId_pieces_id_fk"
		}).onDelete("cascade"),
]);

export const materials = pgTable("materials", {
	id: text().primaryKey().notNull(),
	name: text().notNull(),
	length: real().notNull(),
	width: real().notNull(),
	thickness: real(),
	unit: unit().default('MM').notNull(),
	quantity: integer().default(1).notNull(),
	cost: real(),
	supplier: text(),
	notes: text(),
	createdAt: timestamp({ mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp({ mode: 'string' }).defaultNow().notNull(),
	userId: text().notNull(),
	grainDirection: grainDirection().default('NONE').notNull(),
}, (table) => [
	foreignKey({
			columns: [table.userId],
			foreignColumns: [user.id],
			name: "materials_userId_user_id_fk"
		}).onDelete("cascade"),
]);
