import { relations } from "drizzle-orm/relations";
import { user, session, account, projects, pieces, optimizationResults, materials, optimizationMaterials, sheets, placedPieces } from "./schema";

export const sessionRelations = relations(session, ({one}) => ({
	user: one(user, {
		fields: [session.userId],
		references: [user.id]
	}),
}));

export const userRelations = relations(user, ({many}) => ({
	sessions: many(session),
	accounts: many(account),
	projects: many(projects),
	optimizationResults: many(optimizationResults),
	materials: many(materials),
}));

export const accountRelations = relations(account, ({one}) => ({
	user: one(user, {
		fields: [account.userId],
		references: [user.id]
	}),
}));

export const projectsRelations = relations(projects, ({one, many}) => ({
	user: one(user, {
		fields: [projects.userId],
		references: [user.id]
	}),
	pieces: many(pieces),
	optimizationResults: many(optimizationResults),
}));

export const piecesRelations = relations(pieces, ({one, many}) => ({
	project: one(projects, {
		fields: [pieces.projectId],
		references: [projects.id]
	}),
	placedPieces: many(placedPieces),
}));

export const optimizationResultsRelations = relations(optimizationResults, ({one, many}) => ({
	project: one(projects, {
		fields: [optimizationResults.projectId],
		references: [projects.id]
	}),
	user: one(user, {
		fields: [optimizationResults.userId],
		references: [user.id]
	}),
	optimizationMaterials: many(optimizationMaterials),
	sheets: many(sheets),
}));

export const optimizationMaterialsRelations = relations(optimizationMaterials, ({one}) => ({
	material: one(materials, {
		fields: [optimizationMaterials.materialId],
		references: [materials.id]
	}),
	optimizationResult: one(optimizationResults, {
		fields: [optimizationMaterials.optimizationResultId],
		references: [optimizationResults.id]
	}),
}));

export const materialsRelations = relations(materials, ({one, many}) => ({
	optimizationMaterials: many(optimizationMaterials),
	user: one(user, {
		fields: [materials.userId],
		references: [user.id]
	}),
}));

export const sheetsRelations = relations(sheets, ({one, many}) => ({
	optimizationResult: one(optimizationResults, {
		fields: [sheets.optimizationResultId],
		references: [optimizationResults.id]
	}),
	placedPieces: many(placedPieces),
}));

export const placedPiecesRelations = relations(placedPieces, ({one}) => ({
	sheet: one(sheets, {
		fields: [placedPieces.sheetId],
		references: [sheets.id]
	}),
	piece: one(pieces, {
		fields: [placedPieces.pieceId],
		references: [pieces.id]
	}),
}));