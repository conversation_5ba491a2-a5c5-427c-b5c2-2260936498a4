{"name": "woodworking-optimizer", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "drizzle-kit generate", "db:push": "drizzle-kit push", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio", "db:introspect": "drizzle-kit introspect", "auth:generate": "npx @better-auth/cli@latest generate", "auth:migrate": "npx @better-auth/cli@latest migrate", "type-check": "tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "^3.3.0", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@tanstack/react-query": "^5.28.0", "bcryptjs": "^2.4.3", "better-auth": "^1.2.8", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "d3": "^7.9.0", "dotenv": "^16.5.0", "drizzle-kit": "^0.31.1", "drizzle-orm": "^0.44.1", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.363.0", "nanoid": "^5.0.0", "next": "^14.2.0", "pg": "^8.16.0", "punycode.js": "^2.3.1", "react": "^18.3.0", "react-dom": "^18.3.0", "react-hook-form": "^7.50.0", "tailwind-merge": "^2.2.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.22.0", "zustand": "^4.5.0"}, "devDependencies": {"@tailwindcss/forms": "^0.5.7", "@types/bcryptjs": "^2.4.6", "@types/d3": "^7.4.3", "@types/jsonwebtoken": "^9.0.6", "@types/node": "^20.11.0", "@types/pg": "^8.15.4", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "autoprefixer": "^10.4.0", "eslint": "^8.57.0", "eslint-config-next": "^14.2.0", "postcss": "^8.4.0", "tailwindcss": "^3.4.0", "tsx": "^4.7.0", "typescript": "^5.3.0"}}