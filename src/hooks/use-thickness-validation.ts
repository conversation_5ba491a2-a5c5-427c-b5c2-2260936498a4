import { useMemo } from 'react'
import { 
  validateProjectThickness, 
  validatePieceThickness,
  type MaterialForValidation, 
  type PieceForValidation,
  type ThicknessValidationResult 
} from '@/lib/utils'

interface UseThicknessValidationProps {
  pieces: Array<{
    id: string
    thickness: number | null
    unit: string
  }>
  materials: Array<{
    id: string
    thickness: number | null
    unit: string
  }>
}

interface UseThicknessValidationReturn {
  isProjectValid: boolean
  invalidPieces: Array<{ pieceId: string; validation: ThicknessValidationResult }>
  validPieces: string[]
  getPieceValidation: (pieceId: string) => ThicknessValidationResult | null
  hasThicknessIssues: boolean
  invalidPieceCount: number
}

/**
 * Custom hook for managing thickness validation state
 */
export function useThicknessValidation({
  pieces,
  materials,
}: UseThicknessValidationProps): UseThicknessValidationReturn {
  const validation = useMemo(() => {
    const piecesForValidation: PieceForValidation[] = pieces.map(piece => ({
      id: piece.id,
      thickness: piece.thickness,
      unit: piece.unit,
    }))

    const materialsForValidation: MaterialForValidation[] = materials.map(material => ({
      id: material.id,
      thickness: material.thickness,
      unit: material.unit,
    }))

    return validateProjectThickness(piecesForValidation, materialsForValidation)
  }, [pieces, materials])

  const getPieceValidation = useMemo(() => {
    const validationMap = new Map<string, ThicknessValidationResult>()
    
    // Add invalid pieces to map
    validation.invalidPieces.forEach(({ pieceId, validation: pieceValidation }) => {
      validationMap.set(pieceId, pieceValidation)
    })

    // Add valid pieces to map
    validation.validPieces.forEach(pieceId => {
      const piece = pieces.find(p => p.id === pieceId)
      if (piece) {
        const materialsForValidation: MaterialForValidation[] = materials.map(material => ({
          id: material.id,
          thickness: material.thickness,
          unit: material.unit,
        }))
        
        const pieceValidation = validatePieceThickness(
          { id: piece.id, thickness: piece.thickness, unit: piece.unit },
          materialsForValidation
        )
        validationMap.set(pieceId, pieceValidation)
      }
    })

    return (pieceId: string) => validationMap.get(pieceId) || null
  }, [pieces, materials, validation])

  return {
    isProjectValid: validation.isValid,
    invalidPieces: validation.invalidPieces,
    validPieces: validation.validPieces,
    getPieceValidation,
    hasThicknessIssues: validation.invalidPieces.length > 0,
    invalidPieceCount: validation.invalidPieces.length,
  }
}
