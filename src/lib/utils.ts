import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Generate a unique ID
 */
export function generateId(): string {
  return Math.random().toString(36).substr(2, 9)
}

/**
 * Format number with specified decimal places
 */
export function formatNumber(value: number, decimals: number = 2): string {
  return value.toFixed(decimals)
}

/**
 * Convert units
 */
export type Unit = 'MM' | 'CM' | 'M' | 'IN' | 'FT' | 'mm' | 'cm' | 'm' | 'in' | 'ft'

const UNIT_TO_MM: Record<string, number> = {
  MM: 1,
  CM: 10,
  M: 1000,
  IN: 25.4,
  FT: 304.8,
  mm: 1,
  cm: 10,
  m: 1000,
  in: 25.4,
  ft: 304.8,
}

export function convertToUnit(value: number, fromUnit: Unit, toUnit: Unit): number {
  const valueInMM = value * UNIT_TO_MM[fromUnit]
  return valueInMM / UNIT_TO_MM[toUnit]
}

/**
 * Get piece color from predefined palette
 */
export const PIECE_COLORS = [
  "#e74c3c", "#3498db", "#2ecc71", "#f1c40f", "#9b59b6",
  "#1abc9c", "#e67e22", "#d35400", "#c0392b", "#2980b9",
]

export function getPieceColor(index: number): string {
  return PIECE_COLORS[index % PIECE_COLORS.length]
}

/**
 * Debounce function
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  
  return (...args: Parameters<T>) => {
    if (timeout) {
      clearTimeout(timeout)
    }
    
    timeout = setTimeout(() => {
      func(...args)
    }, wait)
  }
}

/**
 * Format date for display
 */
export function formatDate(date: Date): string {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(date)
}

/**
 * Calculate efficiency percentage
 */
export function calculateEfficiency(usedArea: number, totalArea: number): number {
  if (totalArea === 0) return 0
  return (usedArea / totalArea) * 100
}

/**
 * Validate email format
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * Sleep function for async operations
 */
export function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}

/**
 * Safe JSON parse
 */
export function safeJsonParse<T>(json: string, fallback: T): T {
  try {
    return JSON.parse(json)
  } catch {
    return fallback
  }
}

/**
 * Format file size
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * Clamp number between min and max
 */
export function clamp(value: number, min: number, max: number): number {
  return Math.min(Math.max(value, min), max)
}

/**
 * Thickness validation utilities
 */
export interface ThicknessValidationResult {
  isValid: boolean
  availableThicknesses: number[]
  requiredThickness?: number
  message?: string
}

export interface MaterialForValidation {
  id: string
  thickness: number | null
  unit: string
}

export interface PieceForValidation {
  id: string
  thickness: number | null
  unit: string
}

const THICKNESS_TOLERANCE = 0.1 // 0.1mm tolerance for thickness comparison

/**
 * Check if a piece has matching materials by thickness
 */
export function validatePieceThickness(
  piece: PieceForValidation,
  materials: MaterialForValidation[]
): ThicknessValidationResult {
  // If piece has no thickness specified, it's valid (can use any material)
  if (piece.thickness === null || piece.thickness === undefined) {
    return {
      isValid: true,
      availableThicknesses: [],
    }
  }

  // Convert piece thickness to base unit (MM)
  const pieceThicknessInMM = convertToUnit(piece.thickness, piece.unit as Unit, 'MM')

  // Get all available thicknesses from materials (converted to MM)
  const availableThicknesses = materials
    .filter(material => material.thickness !== null && material.thickness !== undefined)
    .map(material => convertToUnit(material.thickness!, material.unit as Unit, 'MM'))
    .filter((thickness, index, array) => array.indexOf(thickness) === index) // Remove duplicates
    .sort((a, b) => a - b)

  // Check if any material thickness matches the piece thickness (within tolerance)
  const hasMatchingMaterial = availableThicknesses.some(materialThickness =>
    Math.abs(materialThickness - pieceThicknessInMM) <= THICKNESS_TOLERANCE
  )

  if (hasMatchingMaterial) {
    return {
      isValid: true,
      availableThicknesses,
      requiredThickness: pieceThicknessInMM,
    }
  }

  // No matching material found
  const message = availableThicknesses.length > 0
    ? `No materials with ${pieceThicknessInMM.toFixed(1)}mm thickness. Available: ${availableThicknesses.map(t => t.toFixed(1)).join(', ')}mm`
    : 'No materials with thickness specified in inventory'

  return {
    isValid: false,
    availableThicknesses,
    requiredThickness: pieceThicknessInMM,
    message,
  }
}

/**
 * Validate all pieces in a project against available materials
 */
export function validateProjectThickness(
  pieces: PieceForValidation[],
  materials: MaterialForValidation[]
): {
  isValid: boolean
  invalidPieces: Array<{ pieceId: string; validation: ThicknessValidationResult }>
  validPieces: string[]
} {
  const invalidPieces: Array<{ pieceId: string; validation: ThicknessValidationResult }> = []
  const validPieces: string[] = []

  pieces.forEach(piece => {
    const validation = validatePieceThickness(piece, materials)
    if (validation.isValid) {
      validPieces.push(piece.id)
    } else {
      invalidPieces.push({ pieceId: piece.id, validation })
    }
  })

  return {
    isValid: invalidPieces.length === 0,
    invalidPieces,
    validPieces,
  }
}
