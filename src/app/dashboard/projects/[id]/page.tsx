import { auth } from '@/lib/auth'
import { db } from '@/lib/database'
import { projects, pieces, materials, optimizationResults, sheets, placedPieces } from '@/lib/schema'
import { eq, desc, and } from 'drizzle-orm'
import { notFound, redirect } from 'next/navigation'
import { ProjectDetailClient } from '@/components/projects/project-detail-client'
import { headers } from 'next/headers'

interface ProjectDetailPageProps {
  params: {
    id: string
  }
}

// Transform database optimization result to API format
function transformOptimizationResult(dbResult: any) {
  if (!dbResult || !dbResult.sheets) {
    return null
  }

  const layouts = dbResult.sheets.map((sheet: any) => ({
    sheetId: sheet.id,
    baseMaterial: sheet.materialSnapshot,
    pieces: sheet.placedPieces.map((placedPiece: any) => ({
      ...placedPiece.pieceSnapshot,
      x: placedPiece.x,
      y: placedPiece.y,
      packedWidth: placedPiece.packedWidth,
      packedHeight: placedPiece.packedHeight,
      rotation: placedPiece.rotation,
      color: placedPiece.color,
      sheetId: sheet.id,
    })),
    widthUsed: sheet.widthUsed,
    heightUsed: sheet.heightUsed,
    wasteArea: (sheet.materialSnapshot.length * sheet.materialSnapshot.width) - (sheet.widthUsed * sheet.heightUsed),
    efficiency: dbResult.efficiency,
  }))

  return {
    success: true,
    layouts: layouts,
    uncutPieces: dbResult.metadata?.uncutPieces || [],
    metadata: {
      ...dbResult.metadata,
      efficiency: dbResult.efficiency,
      wastePercentage: dbResult.wastePercentage,
      totalSheets: dbResult.totalSheets,
      processingTime: dbResult.processingTime,
      algorithm: dbResult.algorithm,
    }
  }
}

export default async function ProjectDetailPage({ params }: ProjectDetailPageProps) {
  // Get current session
  const session = await auth.api.getSession({
    headers: await headers()
  })

  if (!session) {
    redirect('/auth/login')
  }

  // Initialize default values
  let project: any = null
  let userMaterials: any[] = []
  let latestOptimizationResults: any[] = []

  try {
    // Get project 
    const [projectData] = await db
      .select()
      .from(projects)
      .where(eq(projects.id, params.id))
      .limit(1)

    if (!projectData || projectData.userId !== session.user.id) {
      notFound()
    }
    
    // Get the project pieces
    const projectPieces = await db
      .select()
      .from(pieces)
      .where(eq(pieces.projectId, params.id))

    project = {
      id: projectData.id,
      name: projectData.name,
      description: projectData.description,
      saw_kerf: projectData.sawKerf,
      kerf_unit: projectData.kerfUnit,
      pieces: projectPieces.map(piece => ({
        id: piece.id,
        name: piece.name,
        length: piece.length,
        width: piece.width,
        thickness: piece.thickness,
        unit: piece.unit,
        quantity: piece.quantity,
        grain_direction: piece.grainDirection,
        priority: piece.priority,
        notes: piece.notes
      }))
    }

    // Get user's materials for optimization
    const materialsData = await db
      .select()
      .from(materials)
      .where(eq(materials.userId, session.user.id))
      .orderBy(materials.name)

    userMaterials = materialsData || []

    // Get optimization results
    const optimizationData = await db
      .select()
      .from(optimizationResults)
      .where(eq(optimizationResults.projectId, params.id))
      .orderBy(desc(optimizationResults.createdAt))
      .limit(1)

    // For each optimization result, fetch sheets and placed pieces
    const results = await Promise.all(
      optimizationData.map(async (result) => {
        // Get sheets for this optimization result
        const sheetsData = await db
          .select()
          .from(sheets)
          .where(eq(sheets.optimizationResultId, result.id))
        
        // Get placed pieces for each sheet
        const sheetsWithPieces = await Promise.all(
          sheetsData.map(async (sheet) => {
            const placedPiecesData = await db
              .select()
              .from(placedPieces)
              .where(eq(placedPieces.sheetId, sheet.id))
            
            return {
              ...sheet,
              placedPieces: placedPiecesData
            }
          })
        )
        
        return {
          ...result,
          sheets: sheetsWithPieces
        }
      })
    )

    latestOptimizationResults = results
  } catch (error) {
    console.error('Error fetching project data:', error)
    notFound()
  }

  // Transform the latest optimization result to the expected format
  const transformedOptimizationResult = latestOptimizationResults?.[0]
    ? transformOptimizationResult(latestOptimizationResults[0])
    : null

  return (
    <ProjectDetailClient
      project={project}
      materials={userMaterials || []}
      initialOptimizationResult={transformedOptimizationResult}
    />
  )
}
