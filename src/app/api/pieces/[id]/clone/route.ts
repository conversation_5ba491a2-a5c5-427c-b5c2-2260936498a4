import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/database";
import { pieces, projects } from "@/lib/schema";
import { eq, and } from "drizzle-orm";
import { nanoid } from "nanoid";

// POST /api/pieces/[id]/clone - Clone a piece
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    // Verify authentication
    const session = await auth.api.getSession({
      headers: request.headers,
    });

    if (!session) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 },
      );
    }

    // Get the original piece
    const [originalPiece] = await db
      .select()
      .from(pieces)
      .where(eq(pieces.id, params.id))
      .limit(1);

    if (!originalPiece) {
      return NextResponse.json(
        { success: false, error: "Piece not found" },
        { status: 404 },
      );
    }
    
    // Get the project to verify ownership
    const [project] = await db
      .select()
      .from(projects)
      .where(eq(projects.id, originalPiece.projectId))
      .limit(1);

    if (!project) {
      return NextResponse.json(
        { success: false, error: "Project not found" },
        { status: 404 },
      );
    }

    // Verify that the project belongs to the user
    if (project.userId !== session.user.id) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 403 },
      );
    }

    // Create the cloned piece
    const clonedPiece = await db
      .insert(pieces)
      .values({
        id: nanoid(),
        name: `${originalPiece.name} (Copy)`,
        length: originalPiece.length,
        width: originalPiece.width,
        thickness: originalPiece.thickness,
        unit: originalPiece.unit,
        quantity: originalPiece.quantity,
        grainDirection: originalPiece.grainDirection,
        priority: originalPiece.priority,
        notes: originalPiece.notes,
        projectId: originalPiece.projectId,
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      .returning();

    return NextResponse.json({
      success: true,
      piece: clonedPiece[0],
      message: "Piece cloned successfully",
    });
  } catch (error) {
    console.error("Error cloning piece:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Internal server error",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}
