import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { db } from '@/lib/database';
import { pieces, projects } from '@/lib/schema';
import { eq, and } from 'drizzle-orm';
import { z } from 'zod';

// Validation schema for piece update
const UpdatePieceSchema = z.object({
  name: z.string().min(1, 'Piece name is required').optional(),
  length: z.number().positive('Length must be positive').optional(),
  width: z.number().positive('Width must be positive').optional(),
  thickness: z.number().positive().optional(),
  unit: z.enum(['MM', 'CM', 'M', 'IN', 'FT']).optional(),
  quantity: z.number().int().positive().optional(),
  grainDirection: z.enum(['NONE', 'HORIZONTAL', 'VERTICAL', 'EITHER']).optional(),
  priority: z.number().int().min(1).max(10).optional(),
  notes: z.string().nullable().optional(),
});

// PUT /api/pieces/[id] - Update a piece
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Verify authentication
    const session = await auth.api.getSession({
      headers: request.headers
    });

    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validationResult = UpdatePieceSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid request data',
          details: validationResult.error.errors,
        },
        { status: 400 }
      );
    }

    const updateData = validationResult.data;

    // First, get the piece
    const [piece] = await db
      .select()
      .from(pieces)
      .where(eq(pieces.id, params.id))
      .limit(1);

    if (!piece) {
      return NextResponse.json(
        { success: false, error: 'Piece not found' },
        { status: 404 }
      );
    }

    // Get the project to verify ownership
    const [project] = await db
      .select()
      .from(projects)
      .where(eq(projects.id, piece.projectId))
      .limit(1);
    
    if (!project) {
      return NextResponse.json(
        { success: false, error: 'Project not found' },
        { status: 404 }
      );
    }

    // Verify that the project belongs to the user
    if (project.userId !== session.user.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 403 }
      );
    }

    // Update piece
    const updatedPiece = await db.update(pieces)
      .set({
        ...updateData,
        updatedAt: new Date(),
      })
      .where(eq(pieces.id, params.id))
      .returning();

    return NextResponse.json({
      success: true,
      piece: updatedPiece[0],
    });

  } catch (error) {
    console.error('Error updating piece:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// DELETE /api/pieces/[id] - Delete a piece
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Verify authentication
    const session = await auth.api.getSession({
      headers: request.headers
    });

    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // First, get the piece
    const [piece] = await db
      .select()
      .from(pieces)
      .where(eq(pieces.id, params.id))
      .limit(1);

    if (!piece) {
      return NextResponse.json(
        { success: false, error: 'Piece not found' },
        { status: 404 }
      );
    }

    // Get the project to verify ownership
    const [project] = await db
      .select()
      .from(projects)
      .where(eq(projects.id, piece.projectId))
      .limit(1);
    
    if (!project) {
      return NextResponse.json(
        { success: false, error: 'Project not found' },
        { status: 404 }
      );
    }

    // Verify that the project belongs to the user
    if (project.userId !== session.user.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 403 }
      );
    }

    // Delete piece
    await db.delete(pieces)
      .where(eq(pieces.id, params.id));

    return NextResponse.json({
      success: true,
      message: 'Piece deleted successfully',
    });

  } catch (error) {
    console.error('Error deleting piece:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
