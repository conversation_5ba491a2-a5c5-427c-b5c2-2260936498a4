import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/lib/auth'
import { OptimizationEngine } from '@/lib/optimization/server-engine'
import { db } from '@/lib/database'
import { optimizationResults, sheets, placedPieces, optimizationMaterials } from '@/lib/schema'
import { nanoid } from 'nanoid'
import { z } from 'zod'

// Validation schema for optimization request
const OptimizationRequestSchema = z.object({
  materials: z.array(z.object({
    id: z.string(),
    name: z.string(),
    length: z.number().positive(),
    width: z.number().positive(),
    thickness: z.number().optional(),
    unit: z.enum(['MM', 'CM', 'M', 'IN', 'FT']),
    quantity: z.number().int().positive(),
    cost: z.number().optional(),
  })),
  pieces: z.array(z.object({
    id: z.string(),
    name: z.string(),
    length: z.number().positive(),
    width: z.number().positive(),
    thickness: z.number().optional(),
    unit: z.enum(['MM', 'CM', 'M', 'IN', 'FT']),
    quantity: z.number().int().positive(),
    grainDirection: z.enum(['NONE', 'HORIZONTAL', 'VERTICAL', 'EITHER']).optional(),
    priority: z.number().int().min(1).max(10).optional(),
  })),
  sawKerf: z.number().min(0),
  kerfUnit: z.enum(['MM', 'CM', 'M', 'IN', 'FT']),
  projectId: z.string(),
})

// Database storage function for optimization results
async function saveOptimizationResult(
  result: any,
  projectId: string,
  userId: string,
  processingTime: number,
  materials: any[]
) {
  try {
    // Calculate overall efficiency and waste percentage
    const totalArea = result.layouts.reduce((sum: number, layout: any) =>
      sum + (layout.baseMaterial.length * layout.baseMaterial.width), 0)
    const usedArea = result.layouts.reduce((sum: number, layout: any) =>
      sum + (layout.widthUsed * layout.heightUsed), 0)
    const efficiency = totalArea > 0 ? (usedArea / totalArea) * 100 : 0
    const wastePercentage = 100 - efficiency

    // Create optimization result record
    const optimizationResultId = nanoid()
    const [optimizationRecord] = await db.insert(optimizationResults).values({
      id: optimizationResultId,
      algorithm: 'server-optimized',
      efficiency: efficiency,
      wastePercentage: wastePercentage,
      totalSheets: result.layouts.length,
      totalCost: result.layouts.reduce((sum: number, layout: any) =>
        sum + (layout.baseMaterial.cost || 0), 0),
      processingTime: processingTime,
      metadata: {
        ...result.metadata,
        uncutPieces: result.uncutPieces || [],
        timestamp: new Date().toISOString(),
      },
      projectId: projectId,
      userId: userId,
    }).returning()

    // Save materials used in optimization
    for (const material of materials) {
      await db.insert(optimizationMaterials).values({
        id: nanoid(),
        quantity: material.quantity,
        materialSnapshot: material,
        optimizationResultId: optimizationResultId,
        materialId: material.id,
      })
    }

    // Save sheets and placed pieces
    for (let i = 0; i < result.layouts.length; i++) {
      const layout = result.layouts[i]
      const sheetId = nanoid()

      // Create sheet record
      await db.insert(sheets).values({
        id: sheetId,
        sheetIndex: i,
        widthUsed: layout.widthUsed,
        heightUsed: layout.heightUsed,
        materialSnapshot: layout.baseMaterial,
        optimizationResultId: optimizationResultId,
      })

      // Save placed pieces for this sheet
      for (const piece of layout.pieces) {
        await db.insert(placedPieces).values({
          id: nanoid(),
          x: piece.x,
          y: piece.y,
          packedWidth: piece.packedWidth,
          packedHeight: piece.packedHeight,
          rotation: piece.rotation,
          color: piece.color,
          pieceSnapshot: {
            id: piece.id,
            name: piece.name,
            length: piece.length,
            width: piece.width,
            thickness: piece.thickness,
            unit: piece.unit,
            quantity: piece.quantity,
            grainDirection: piece.grainDirection,
            priority: piece.priority,
          },
          sheetId: sheetId,
          pieceId: piece.id,
        })
      }
    }

    return optimizationRecord
  } catch (error) {
    console.error('Error saving optimization result:', error)
    throw error
  }
}

export async function POST(request: NextRequest) {
  try {
    // Verify authentication
    const session = await auth.api.getSession({
      headers: request.headers
    })

    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Parse and validate request body
    const body = await request.json()
    const validationResult = OptimizationRequestSchema.safeParse(body)

    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid request data',
          details: validationResult.error.errors,
        },
        { status: 400 }
      )
    }

    const optimizationRequest = validationResult.data

    // Rate limiting check (simple implementation)
    const userAgent = request.headers.get('user-agent') || ''
    const clientIP = request.headers.get('x-forwarded-for') || 'unknown'
    
    // TODO: Implement proper rate limiting with Redis or similar
    console.log(`Optimization request from ${clientIP}, User-Agent: ${userAgent}`)

    // Initialize optimization engine
    const engine = new OptimizationEngine()

    // Perform optimization
    const startTime = Date.now()
    const result = await engine.optimize({
      ...optimizationRequest,
      userId: session.user.id,
    })

    const processingTime = Date.now() - startTime

    // Store optimization result in database if successful
    if (result.success && result.layouts && result.layouts.length > 0) {
      try {
        await saveOptimizationResult(
          result,
          optimizationRequest.projectId,
          session.user.id,
          processingTime,
          optimizationRequest.materials
        )
      } catch (dbError) {
        console.error('Failed to save optimization result to database:', dbError)
        // Continue with response even if database save fails
      }
    }

    // Return result
    return NextResponse.json({
      success: result.success,
      layouts: result.layouts,
      uncutPieces: result.uncutPieces,
      metadata: {
        ...result.metadata,
        processingTime,
        algorithm: 'server-optimized',
        timestamp: new Date().toISOString(),
      },
      error: result.error,
    })

  } catch (error) {
    console.error('Optimization API error:', error)
    
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error during optimization',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    )
  }
}

// Database storage functions can be implemented here later if needed

// Handle OPTIONS for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
