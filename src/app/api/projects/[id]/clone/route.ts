import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/database";
import { projects, pieces } from "@/lib/schema";
import { eq, and } from "drizzle-orm";
import { nanoid } from "nanoid";

// POST /api/projects/[id]/clone - Clone a project with all its pieces
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } },
) {
  try {
    // Verify authentication
    const session = await auth.api.getSession({
      headers: request.headers,
    });

    if (!session) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 },
      );
    }

    // Get the original project
    const [originalProject] = await db
      .select()
      .from(projects)
      .where(
        and(
          eq(projects.id, params.id),
          eq(projects.userId, session.user.id)
        )
      )
      .limit(1);

    if (!originalProject) {
      return NextResponse.json(
        { success: false, error: "Project not found" },
        { status: 404 },
      );
    }
    
    // Get all pieces for this project
    const projectPieces = await db
      .select()
      .from(pieces)
      .where(eq(pieces.projectId, params.id));

    // Create the cloned project
    const newProjectId = nanoid();
    const clonedProject = await db
      .insert(projects)
      .values({
        id: newProjectId,
        name: `${originalProject.name} (Copy)`,
        description: originalProject.description,
        sawKerf: originalProject.sawKerf,
        kerfUnit: originalProject.kerfUnit,
        userId: session.user.id,
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      .returning();

    // Clone all pieces if they exist
    if (projectPieces && projectPieces.length > 0) {
      const clonedPieces = projectPieces.map((piece) => ({
        id: nanoid(),
        name: piece.name,
        length: piece.length,
        width: piece.width,
        thickness: piece.thickness,
        unit: piece.unit,
        quantity: piece.quantity,
        grainDirection: piece.grainDirection,
        priority: piece.priority,
        notes: piece.notes,
        projectId: newProjectId,
        createdAt: new Date(),
        updatedAt: new Date(),
      }));

      await db.insert(pieces).values(clonedPieces);
    }

    return NextResponse.json({
      success: true,
      project: clonedProject[0],
      message: "Project cloned successfully",
    });
  } catch (error) {
    console.error("Error cloning project:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Internal server error",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}
