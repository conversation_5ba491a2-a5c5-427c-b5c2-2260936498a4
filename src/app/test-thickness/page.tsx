'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { validatePieceThickness, validateProjectThickness } from '@/lib/utils'
import { AlertTriangle, CheckCircle } from 'lucide-react'

export default function ThicknessTestPage() {
  const [testResults, setTestResults] = useState<any[]>([])

  const materials = [
    { id: '1', thickness: 18, unit: 'MM' },
    { id: '2', thickness: 25, unit: 'MM' },
    { id: '3', thickness: 0.75, unit: 'IN' }, // ~19.05mm
    { id: '4', thickness: null, unit: 'MM' },
  ]

  const testPieces = [
    { id: 'p1', thickness: 18, unit: 'MM', name: 'Valid 18mm piece' },
    { id: 'p2', thickness: 12, unit: 'MM', name: 'Invalid 12mm piece' },
    { id: 'p3', thickness: 25, unit: 'MM', name: 'Valid 25mm piece' },
    { id: 'p4', thickness: 0.75, unit: 'IN', name: 'Valid 0.75" piece' },
    { id: 'p5', thickness: null, unit: 'MM', name: 'No thickness piece' },
    { id: 'p6', thickness: 30, unit: 'MM', name: 'Invalid 30mm piece' },
  ]

  const runTests = () => {
    const results = testPieces.map(piece => {
      const validation = validatePieceThickness(piece, materials)
      return {
        piece,
        validation
      }
    })

    const projectValidation = validateProjectThickness(testPieces, materials)

    setTestResults([...results, { projectValidation }])
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Thickness Validation Test</CardTitle>
          <CardDescription>
            Test the thickness validation logic with sample pieces and materials
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h3 className="font-semibold mb-2">Available Materials:</h3>
            <ul className="list-disc list-inside space-y-1 text-sm">
              {materials.map(material => (
                <li key={material.id}>
                  {material.thickness ? `${material.thickness}${material.unit}` : 'No thickness specified'}
                </li>
              ))}
            </ul>
          </div>

          <div>
            <h3 className="font-semibold mb-2">Test Pieces:</h3>
            <ul className="list-disc list-inside space-y-1 text-sm">
              {testPieces.map(piece => (
                <li key={piece.id}>
                  {piece.name}: {piece.thickness ? `${piece.thickness}${piece.unit}` : 'No thickness'}
                </li>
              ))}
            </ul>
          </div>

          <Button onClick={runTests}>Run Validation Tests</Button>

          {testResults.length > 0 && (
            <div className="space-y-4">
              <h3 className="font-semibold">Test Results:</h3>
              
              {testResults.slice(0, -1).map((result, index) => (
                <Alert key={index} variant={result.validation.isValid ? 'default' : 'destructive'}>
                  {result.validation.isValid ? (
                    <CheckCircle className="h-4 w-4" />
                  ) : (
                    <AlertTriangle className="h-4 w-4" />
                  )}
                  <AlertDescription>
                    <strong>{result.piece.name}:</strong>{' '}
                    {result.validation.isValid ? (
                      'Valid - Can be cut with available materials'
                    ) : (
                      result.validation.message
                    )}
                  </AlertDescription>
                </Alert>
              ))}

              {testResults[testResults.length - 1]?.projectValidation && (
                <Alert variant={testResults[testResults.length - 1].projectValidation.isValid ? 'default' : 'destructive'}>
                  {testResults[testResults.length - 1].projectValidation.isValid ? (
                    <CheckCircle className="h-4 w-4" />
                  ) : (
                    <AlertTriangle className="h-4 w-4" />
                  )}
                  <AlertDescription>
                    <strong>Project Validation:</strong>{' '}
                    {testResults[testResults.length - 1].projectValidation.isValid ? (
                      'All pieces can be cut with available materials'
                    ) : (
                      `${testResults[testResults.length - 1].projectValidation.invalidPieces.length} piece(s) have thickness compatibility issues`
                    )}
                  </AlertDescription>
                </Alert>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
