'use client'

import { useState, useCallback } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { ArrowLeft, Zap, FileText, Eye, Plus, Settings } from 'lucide-react'
import Link from 'next/link'
import { useToast } from '@/hooks/use-toast'
import { useThicknessValidation } from '@/hooks/use-thickness-validation'
import { formatNumber } from '@/lib/utils'
import { PieceManager } from './piece-manager'
import { CuttingListView } from './cutting-list-view'
import { VisualizationView } from './visualization-view'

interface ProjectDetailClientProps {
  project: {
    id: string
    name: string
    description: string | null
    saw_kerf: number
    kerf_unit: string
    pieces: Array<{
      id: string
      name: string
      length: number
      width: number
      thickness: number | null
      unit: string
      quantity: number
      grain_direction: string
      priority: number
      notes: string | null
    }>
  }
  materials: Array<{
    id: string
    name: string
    length: number
    width: number
    thickness: number | null
    unit: string
    quantity: number
    cost: number | null
  }>
  initialOptimizationResult: any
}

export function ProjectDetailClient({
  project,
  materials,
  initialOptimizationResult
}: ProjectDetailClientProps) {
  const [optimizing, setOptimizing] = useState(false)
  const [optimizationResult, setOptimizationResult] = useState(initialOptimizationResult)
  const [activeTab, setActiveTab] = useState('pieces')
  const { toast } = useToast()

  // Thickness validation
  const thicknessValidation = useThicknessValidation({
    pieces: project.pieces,
    materials,
  })

  const handleOptimize = useCallback(async () => {
    if (project.pieces.length === 0) {
      toast({
        title: 'No pieces to optimize',
        description: 'Please add some pieces to your project before optimizing.',
        variant: 'destructive',
      })
      return
    }

    if (materials.length === 0) {
      toast({
        title: 'No materials available',
        description: 'Please add materials to your inventory before optimizing.',
        variant: 'destructive',
      })
      return
    }

    // Check for thickness compatibility issues
    if (thicknessValidation.hasThicknessIssues) {
      toast({
        title: 'Thickness compatibility issues',
        description: `${thicknessValidation.invalidPieceCount} piece(s) cannot be cut with available materials due to thickness mismatches. Please add matching materials to your inventory or adjust piece thicknesses.`,
        variant: 'destructive',
      })
      return
    }

    setOptimizing(true)

    try {
      const response = await fetch('/api/optimize', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          materials,
          pieces: project.pieces,
          sawKerf: project.saw_kerf,
          kerfUnit: project.kerf_unit,
          projectId: project.id,
        }),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Optimization failed')
      }

      if (result.success) {
        setOptimizationResult(result)
        setActiveTab('visualization')

        // Create toast message with uncut pieces warning if any
        let description = `Generated ${result.layouts.length} sheet(s) with ${result.metadata.efficiency.toFixed(1)}% efficiency.`
        if (result.uncutPieces && result.uncutPieces.length > 0) {
          const uncutCount = result.uncutPieces.reduce((sum: number, piece: any) => sum + piece.quantity, 0)
          const thicknessMismatches = result.metadata?.thicknessMismatches || 0
          description += ` Warning: ${uncutCount} piece(s) could not be cut`
          if (thicknessMismatches > 0) {
            description += ` (${thicknessMismatches} due to thickness mismatch)`
          }
          description += `.`
        }

        toast({
          title: 'Optimization complete!',
          description,
          variant: result.uncutPieces && result.uncutPieces.length > 0 ? 'destructive' : 'default',
        })
      } else {
        throw new Error(result.error || 'Optimization failed')
      }
    } catch (error) {
      console.error('Optimization error:', error)
      toast({
        title: 'Optimization failed',
        description: error instanceof Error ? error.message : 'An unexpected error occurred',
        variant: 'destructive',
      })
    } finally {
      setOptimizing(false)
    }
  }, [project, materials, toast, thicknessValidation.hasThicknessIssues, thicknessValidation.invalidPieceCount])

  // Function to generate SVG visualization for a layout
  const generateLayoutSVG = useCallback((layout: any, sheetIndex: number) => {
    // Use larger print dimensions for better quality
    const printWidth = 800 // Increased from 600 for better print quality
    const margin = { top: 20, right: 20, bottom: 60, left: 70 }

    // Calculate scale to maximize width utilization (same as interactive view)
    const sheetWidth = layout.widthUsed
    const sheetHeight = layout.heightUsed
    const availableWidth = printWidth - margin.left - margin.right

    // Prioritize full width utilization - calculate scale based on width only
    const widthBasedScale = availableWidth / sheetWidth
    const autoScale = widthBasedScale

    // Calculate the resulting height based on the width-optimized scale
    const scaledHeight = sheetHeight * autoScale
    const containerHeight = scaledHeight + margin.top + margin.bottom

    // Helper function to get piece color
    const getPieceColor = (index: number) => {
      const colors = [
        '#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6',
        '#06b6d4', '#84cc16', '#f97316', '#ec4899', '#6366f1'
      ]
      return colors[index % colors.length]
    }

    // Generate SVG content
    let svgContent = `
      <svg width="${printWidth}" height="${containerHeight}" xmlns="http://www.w3.org/2000/svg">
        <g transform="translate(${margin.left}, ${margin.top})">
          <!-- Sheet background -->
          <rect x="0" y="0" width="${sheetWidth * autoScale}" height="${sheetHeight * autoScale}"
                fill="#f8f9fa" stroke="#dee2e6" stroke-width="2" rx="4"/>

          <!-- Grid lines -->
          <g opacity="0.1">
    `

    // Add grid lines
    const gridSpacing = 50 // mm
    for (let x = 0; x <= sheetWidth; x += gridSpacing) {
      svgContent += `<line x1="${x * autoScale}" y1="0" x2="${x * autoScale}" y2="${sheetHeight * autoScale}" stroke="#6b7280" stroke-width="0.5"/>`
    }
    for (let y = 0; y <= sheetHeight; y += gridSpacing) {
      svgContent += `<line x1="0" y1="${y * autoScale}" x2="${sheetWidth * autoScale}" y2="${y * autoScale}" stroke="#6b7280" stroke-width="0.5"/>`
    }

    svgContent += `</g>`

    // Add pieces
    layout.pieces.forEach((piece: any, index: number) => {
      const x = piece.x * autoScale
      const y = piece.y * autoScale
      const width = piece.packedWidth * autoScale
      const height = piece.packedHeight * autoScale
      const color = piece.color || getPieceColor(index)

      svgContent += `
        <g transform="translate(${x}, ${y})">
          <rect width="${width}" height="${height}" fill="${color}" stroke="#ffffff" stroke-width="1" rx="2"/>
          <text x="${width / 2}" y="${height / 2}" text-anchor="middle" dominant-baseline="middle"
                font-family="Arial, sans-serif" font-size="${Math.max(8, 10 * autoScale)}px"
                font-weight="500" fill="#1f2937" pointer-events="none">
            ${piece.name.length > Math.floor(width / 6) ? piece.name.substring(0, Math.floor(width / 6) - 3) + '...' : piece.name}
          </text>
          ${width > 50 && height > 40 ? `
            <!-- Width dimension label (bottom edge, inside) -->
            <text x="${width / 2}" y="${height - 8}" text-anchor="middle" dominant-baseline="baseline"
                  font-family="Arial, sans-serif" font-size="${Math.max(9, 11 * autoScale)}px"
                  font-weight="600" fill="#1f2937" pointer-events="none"
                  style="text-shadow: 1px 1px 2px rgba(255,255,255,0.8)">
              ${formatNumber(piece.packedWidth)}
            </text>
            <!-- Height dimension label (right edge, inside) -->
            <text x="${width - 8}" y="${height / 2}" text-anchor="middle" dominant-baseline="middle"
                  font-family="Arial, sans-serif" font-size="${Math.max(9, 11 * autoScale)}px"
                  font-weight="600" fill="#1f2937" pointer-events="none"
                  style="text-shadow: 1px 1px 2px rgba(255,255,255,0.8)"
                  transform="rotate(90, ${width - 8}, ${height / 2})">
              ${formatNumber(piece.packedHeight)}
            </text>
          ` : ''}
        </g>
      `
    })

    // Add axis dimensions
    const xAxisSpacing = Math.max(100, Math.ceil(sheetWidth / 8 / 50) * 50) // Dynamic spacing based on sheet width
    for (let x = 0; x <= sheetWidth; x += xAxisSpacing) {
      // Tick marks
      svgContent += `<line x1="${x * autoScale}" y1="${sheetHeight * autoScale}" x2="${x * autoScale}" y2="${sheetHeight * autoScale + 8}" stroke="#374151" stroke-width="1"/>`

      // Labels
      svgContent += `<text x="${x * autoScale}" y="${sheetHeight * autoScale + 20}" text-anchor="middle" font-family="Arial, sans-serif" font-size="${Math.max(10, 11 * autoScale)}px" fill="#374151">${formatNumber(x)}</text>`
    }

    // Y-axis dimensions (left)
    const yAxisSpacing = Math.max(100, Math.ceil(sheetHeight / 6 / 50) * 50) // Dynamic spacing based on sheet height
    for (let y = 0; y <= sheetHeight; y += yAxisSpacing) {
      // Tick marks
      svgContent += `<line x1="-8" y1="${y * autoScale}" x2="0" y2="${y * autoScale}" stroke="#374151" stroke-width="1"/>`

      // Labels
      svgContent += `<text x="-12" y="${y * autoScale}" text-anchor="end" dominant-baseline="middle" font-family="Arial, sans-serif" font-size="${Math.max(10, 11 * autoScale)}px" fill="#374151">${formatNumber(sheetHeight - y)}</text>`

      // Add Y-axis line if it's the first tick
      if (y === 0) {
        svgContent += `<line x1="0" y1="0" x2="0" y2="${sheetHeight * autoScale}" stroke="#374151" stroke-width="1"/>`
      }
    }

    // Add X-axis line
    svgContent += `<line x1="0" y1="${sheetHeight * autoScale}" x2="${sheetWidth * autoScale}" y2="${sheetHeight * autoScale}" stroke="#374151" stroke-width="1"/>`

    // Add axis labels
    svgContent += `<text x="${(sheetWidth * autoScale) / 2}" y="${sheetHeight * autoScale + 40}" text-anchor="middle" font-family="Arial, sans-serif" font-size="${Math.max(12, 13 * autoScale)}px" font-weight="600" fill="#1f2937">Width (${layout.baseMaterial.unit})</text>`

    svgContent += `<text x="-50" y="${(sheetHeight * autoScale) / 2}" text-anchor="middle" font-family="Arial, sans-serif" font-size="${Math.max(12, 13 * autoScale)}px" font-weight="600" fill="#1f2937" transform="rotate(-90, -50, ${(sheetHeight * autoScale) / 2})">Height (${layout.baseMaterial.unit})</text>`

    // Add piece position indicators on axes
    layout.pieces.forEach((piece: any, index: number) => {
      const pieceColor = piece.color || getPieceColor(index)

      // X-axis indicators (bottom) - start and end positions
      const xStart = piece.x * autoScale
      const xEnd = (piece.x + piece.packedWidth) * autoScale

      // Start position indicator
      svgContent += `<line x1="${xStart}" y1="${sheetHeight * autoScale + 2}" x2="${xStart}" y2="${sheetHeight * autoScale + 6}" stroke="${pieceColor}" stroke-width="2"/>`

      // End position indicator
      svgContent += `<line x1="${xEnd}" y1="${sheetHeight * autoScale + 2}" x2="${xEnd}" y2="${sheetHeight * autoScale + 6}" stroke="${pieceColor}" stroke-width="2"/>`

      // Position labels on X-axis
      if (autoScale > 0.5) { // Only show labels when zoomed in enough
        svgContent += `<text x="${xStart}" y="${sheetHeight * autoScale + 35}" text-anchor="middle" font-family="Arial, sans-serif" font-size="${Math.max(8, 9 * autoScale)}px" fill="${pieceColor}" font-weight="600">${formatNumber(piece.x)}</text>`

        svgContent += `<text x="${xEnd}" y="${sheetHeight * autoScale + 35}" text-anchor="middle" font-family="Arial, sans-serif" font-size="${Math.max(8, 9 * autoScale)}px" fill="${pieceColor}" font-weight="600">${formatNumber(piece.x + piece.packedWidth)}</text>`
      }

      // Y-axis indicators (left) - start and end positions
      const yStart = piece.y * autoScale
      const yEnd = (piece.y + piece.packedHeight) * autoScale

      // Start position indicator
      svgContent += `<line x1="-6" y1="${yStart}" x2="-2" y2="${yStart}" stroke="${pieceColor}" stroke-width="2"/>`

      // End position indicator
      svgContent += `<line x1="-6" y1="${yEnd}" x2="-2" y2="${yEnd}" stroke="${pieceColor}" stroke-width="2"/>`

      // Position labels on Y-axis
      if (autoScale > 0.5) { // Only show labels when zoomed in enough
        svgContent += `<text x="-35" y="${yStart}" text-anchor="middle" dominant-baseline="middle" font-family="Arial, sans-serif" font-size="${Math.max(8, 9 * autoScale)}px" fill="${pieceColor}" font-weight="600">${formatNumber(sheetHeight - piece.y)}</text>`

        svgContent += `<text x="-35" y="${yEnd}" text-anchor="middle" dominant-baseline="middle" font-family="Arial, sans-serif" font-size="${Math.max(8, 9 * autoScale)}px" fill="${pieceColor}" font-weight="600">${formatNumber(sheetHeight - (piece.y + piece.packedHeight))}</text>`
      }
    })

    svgContent += `
        </g>
      </svg>
    `

    return svgContent
  }, [])

  const handlePrint = useCallback(() => {
    if (!optimizationResult?.layouts || optimizationResult.layouts.length === 0) {
      toast({
        title: 'No cutting plan to print',
        description: 'Please run optimization first.',
        variant: 'destructive',
      })
      return
    }

    // Generate print content
    const printWindow = window.open('', '_blank')
    if (printWindow) {
      printWindow.document.write(`
        <html>
          <head>
            <title>${project.name} - Cutting Plan</title>
            <style>
              body {
                font-family: Arial, sans-serif;
                margin: 0;
                padding: 20px;
                line-height: 1.4;
              }
              .header {
                border-bottom: 2px solid #333;
                padding-bottom: 15px;
                margin-bottom: 25px;
              }
              .sheet-page {
                page-break-before: always;
                page-break-inside: avoid;
                margin-bottom: 20px;
                min-height: auto;
                display: flex;
                flex-direction: column;
                max-height: none;
              }
              .sheet-page:first-child {
                page-break-before: auto;
              }
              .sheet-header {
                margin-bottom: 20px;
                border-bottom: 1px solid #ccc;
                padding-bottom: 10px;
              }
              .sheet-content {
                display: flex;
                gap: 20px;
                flex: 1;
                align-items: flex-start;
              }
              .sheet-info {
                flex: 1;
                min-width: 300px;
                max-width: 400px;
              }
              .sheet-visualization {
                flex: 2;
                display: flex;
                justify-content: flex-start;
                align-items: flex-start;
                overflow: visible;
              }
              .piece {
                margin: 3px 0;
                padding: 2px 0;
                font-size: 14px;
              }
              .piece-list {
                max-height: 400px;
                overflow: visible;
              }
              .warning-box {
                color: #dc2626;
                font-weight: bold;
                margin-top: 10px;
                padding: 10px;
                border: 2px solid #dc2626;
                background-color: #fef2f2;
                border-radius: 4px;
              }
              .uncut-section {
                border: 2px solid #dc2626;
                background-color: #fef2f2;
                padding: 15px;
                border-radius: 4px;
                margin-top: 20px;
              }
              .solutions-box {
                margin-top: 15px;
                padding: 10px;
                background-color: #fef3c7;
                border: 1px solid #f59e0b;
                border-radius: 4px;
              }
              .solutions-box ul {
                margin: 5px 0;
                padding-left: 20px;
              }
              .solutions-box li {
                margin: 3px 0;
              }
              @media print {
                body {
                  margin: 0;
                  padding: 15px;
                  font-size: 12px;
                }
                .sheet-page {
                  page-break-before: always;
                  page-break-inside: avoid;
                  margin: 0 0 20px 0;
                  min-height: auto;
                  max-height: none;
                  display: flex;
                  flex-direction: column;
                }
                .sheet-page:first-child {
                  page-break-before: auto;
                }
                .sheet-header {
                  page-break-after: avoid;
                  margin-bottom: 15px;
                }
                .sheet-content {
                  flex-direction: column;
                  gap: 15px;
                  page-break-inside: avoid;
                }
                .sheet-visualization {
                  width: 100%;
                  justify-content: center;
                  page-break-inside: avoid;
                }
                .sheet-visualization svg {
                  max-width: 100%;
                  height: auto;
                }
              }
            </style>
          </head>
          <body>
            <div class="header">
              <h1>${project.name} - Cutting Plan</h1>
              <p><strong>Generated:</strong> ${new Date().toLocaleDateString()}</p>
              <p><strong>Total Sheets:</strong> ${optimizationResult.layouts.length}</p>
              <p><strong>Overall Efficiency:</strong> ${optimizationResult.metadata?.efficiency?.toFixed(1) || 'N/A'}%</p>
              ${optimizationResult.uncutPieces && optimizationResult.uncutPieces.length > 0 ? `
                <div class="warning-box">
                  ⚠️ WARNING: ${optimizationResult.uncutPieces.reduce((sum: number, piece: any) => sum + piece.quantity, 0)} piece(s) could not be cut with available materials
                </div>
              ` : ''}
            </div>

            ${optimizationResult.layouts.map((layout: any, index: number) => `
              <div class="sheet-page">
                <div class="sheet-header">
                  <h2>Sheet ${index + 1}: ${layout.baseMaterial.name}</h2>
                  <p><strong>Material Size:</strong> ${layout.widthUsed} × ${layout.heightUsed} ${layout.baseMaterial.unit} •
                     <strong>Efficiency:</strong> ${layout.efficiency.toFixed(1)}% •
                     <strong>Pieces:</strong> ${layout.pieces.length}</p>
                </div>

                <div class="sheet-content">
                  <div class="sheet-info">
                    <h3>Pieces to Cut:</h3>
                    <div class="piece-list">
                      ${layout.pieces.map((piece: any, pieceIndex: number) => `
                        <div class="piece">
                          ${pieceIndex + 1}. <strong>${piece.name}</strong> - ${piece.packedWidth} × ${piece.packedHeight} ${piece.unit}
                          ${piece.rotation > 0 ? ` <em>(rotated ${piece.rotation}°)</em>` : ''}
                        </div>
                      `).join('')}
                    </div>
                  </div>

                  <div class="sheet-visualization">
                    ${generateLayoutSVG(layout, index)}
                  </div>
                </div>
              </div>
            `).join('')}

            ${optimizationResult.uncutPieces && optimizationResult.uncutPieces.length > 0 ? `
              <div class="sheet-page">
                <div class="uncut-section">
                  <h2 style="color: #dc2626;">⚠️ UNCUT PIECES</h2>
                  <p style="color: #dc2626; font-weight: bold;">The following pieces could not be cut:</p>
                  ${optimizationResult.uncutPieces.map((piece: any, index: number) => `
                    <div class="piece" style="color: #dc2626; margin-bottom: 8px;">
                      ${index + 1}. <strong>${piece.name}</strong> - ${piece.length} × ${piece.width}${piece.thickness ? ` × ${piece.thickness}` : ''} ${piece.unit}
                      ${piece.quantity > 1 ? ` (Quantity: ${piece.quantity})` : ''}
                      <div style="font-size: 0.9em; color: #7f1d1d; margin-left: 15px;">
                        ${piece.reason === 'thickness_mismatch' ?
                          `❌ No materials with ${piece.requiredThickness}mm thickness${piece.availableThicknesses && piece.availableThicknesses.length > 0 ? ` (Available: ${piece.availableThicknesses.join(', ')}mm)` : ''}` :
                          piece.reason === 'size_too_large' ?
                          '📏 Too large for materials with matching thickness' :
                          '⚠️ No suitable materials available'
                        }
                      </div>
                    </div>
                  `).join('')}

                  <div class="solutions-box">
                    <strong>Possible Solutions:</strong>
                    <ul>
                      <li>Add materials with matching thickness to inventory</li>
                      <li>Add larger material sheets to inventory</li>
                      <li>Increase quantity of existing materials</li>
                      <li>Split large pieces into smaller components</li>
                      <li>Check if pieces can be rotated (grain direction permitting)</li>
                    </ul>
                  </div>
                </div>
              </div>
            ` : ''}
          </body>
        </html>
      `)
      printWindow.document.close()
      printWindow.print()
    }
  }, [optimizationResult, project.name, toast, generateLayoutSVG])

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/dashboard/projects">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Projects
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{project.name}</h1>
            {project.description && (
              <p className="text-gray-600 mt-1">{project.description}</p>
            )}
            <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
              <span>{project.pieces.length} pieces</span>
              <span>Saw kerf: {project.saw_kerf}{project.kerf_unit}</span>
            </div>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            onClick={handleOptimize}
            disabled={optimizing || thicknessValidation.hasThicknessIssues}
            className="bg-green-600 hover:bg-green-700 disabled:bg-gray-400"
            title={thicknessValidation.hasThicknessIssues ? 'Cannot optimize: Some pieces have thickness compatibility issues' : undefined}
          >
            {optimizing ? (
              'Optimizing...'
            ) : thicknessValidation.hasThicknessIssues ? (
              <>
                <Zap className="h-4 w-4 mr-2" />
                Optimize (Disabled)
              </>
            ) : (
              <>
                <Zap className="h-4 w-4 mr-2" />
                Optimize
              </>
            )}
          </Button>
          <Button variant="outline" onClick={handlePrint}>
            <FileText className="h-4 w-4 mr-2" />
            Print
          </Button>
          <Button variant="outline" asChild>
            <Link href={`/dashboard/projects/${project.id}/settings`}>
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Link>
          </Button>
        </div>
      </div>

      {/* Optimization Status */}
      {optimizationResult && (
        <Alert variant={optimizationResult.uncutPieces && optimizationResult.uncutPieces.length > 0 ? 'destructive' : 'default'}>
          <Zap className="h-4 w-4" />
          <AlertDescription>
            Last optimization: {optimizationResult.layouts.length} sheet(s) with{' '}
            {optimizationResult.metadata?.efficiency?.toFixed(1) || 'N/A'}% efficiency
            {optimizationResult.metadata?.processingTime && (
              <> in {optimizationResult.metadata.processingTime}ms</>
            )}
            {optimizationResult.uncutPieces && optimizationResult.uncutPieces.length > 0 && (
              <>
                <br />
                <strong>Warning:</strong> {optimizationResult.uncutPieces.reduce((sum: number, piece: any) => sum + piece.quantity, 0)} piece(s) could not be cut
                {optimizationResult.metadata?.thicknessMismatches > 0 && (
                  <> ({optimizationResult.metadata.thicknessMismatches} due to thickness mismatch)</>
                )}
              </>
            )}
          </AlertDescription>
        </Alert>
      )}

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="pieces">
            <Plus className="h-4 w-4 mr-2" />
            Pieces
          </TabsTrigger>
          <TabsTrigger value="cutting-list">
            <FileText className="h-4 w-4 mr-2" />
            Cutting List
          </TabsTrigger>
          <TabsTrigger value="visualization">
            <Eye className="h-4 w-4 mr-2" />
            Visualization
          </TabsTrigger>
        </TabsList>

        <TabsContent value="pieces" className="space-y-6">
          <PieceManager
            projectId={project.id}
            pieces={project.pieces}
            materials={materials}
          />
        </TabsContent>

        <TabsContent value="cutting-list" className="space-y-6">
          <CuttingListView 
            project={project}
            optimizationResult={optimizationResult}
          />
        </TabsContent>

        <TabsContent value="visualization" className="space-y-6">
          <VisualizationView 
            optimizationResult={optimizationResult}
            onOptimize={handleOptimize}
            optimizing={optimizing}
          />
        </TabsContent>
      </Tabs>
    </div>
  )
}
