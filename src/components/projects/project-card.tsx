"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Setting<PERSON>, Copy, Calendar } from "lucide-react";
import Link from "next/link";
import { formatDate } from "@/lib/utils";
import { useToast } from "@/hooks/use-toast";

interface Project {
  id: string;
  name: string;
  description: string | null;
  sawKerf: number;
  kerfUnit: string;
  createdAt: Date;
  updatedAt: Date;
  pieceCount: number;
}

interface ProjectCardProps {
  project: Project;
}

export function ProjectCard({ project }: ProjectCardProps) {
  const [cloning, setCloning] = useState(false);
  const router = useRouter();
  const { toast } = useToast();

  const handleClone = async () => {
    setCloning(true);

    try {
      const response = await fetch(`/api/projects/${project.id}/clone`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "Failed to clone project");
      }

      if (result.success) {
        toast({
          title: "Project cloned",
          description: "Your project has been cloned successfully.",
        });
        router.refresh();
      } else {
        throw new Error(result.error || "Failed to clone project");
      }
    } catch (err) {
      console.error("Error cloning project:", err);
      toast({
        title: "Error",
        description: err instanceof Error ? err.message : "Failed to clone project",
        variant: "destructive",
      });
    } finally {
      setCloning(false);
    }
  };

  return (
    <Card className="hover:shadow-lg transition-shadow cursor-pointer">
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg">{project.name}</CardTitle>
            {project.description && (
              <CardDescription className="mt-2">
                {project.description}
              </CardDescription>
            )}
          </div>
          <div className="flex space-x-1">
            <Button variant="ghost" size="sm" asChild>
              <Link href={`/dashboard/projects/${project.id}/settings`}>
                <Settings className="h-4 w-4" />
              </Link>
            </Button>
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={handleClone}
              disabled={cloning}
              title="Clone project"
            >
              <Copy className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {/* Project Stats */}
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">Pieces:</span>
            <span className="font-medium">{project.pieceCount || 0}</span>
          </div>

          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">Saw Kerf:</span>
            <span className="font-medium">{project.sawKerf}{project.kerfUnit}</span>
          </div>

          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">Updated:</span>
            <span className="font-medium">{formatDate(project.updatedAt)}</span>
          </div>

          {/* Action Button */}
          <div className="pt-3 border-t">
            <Button asChild className="w-full">
              <Link href={`/dashboard/projects/${project.id}`}>
                <Calendar className="h-4 w-4 mr-2" />
                Open Project
              </Link>
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
