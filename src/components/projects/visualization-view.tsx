'use client'

import { useEffect, useRef, useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Eye, ChevronLeft, ChevronRight, Zap, RotateCcw, Package } from 'lucide-react'
import * as d3 from 'd3'
import { formatNumber, getPieceColor } from '@/lib/utils'

interface VisualizationViewProps {
  optimizationResult: any
  onOptimize: () => void
  optimizing: boolean
}

export function VisualizationView({ 
  optimizationResult, 
  onOptimize, 
  optimizing 
}: VisualizationViewProps) {
  const svgRef = useRef<SVGSVGElement>(null)
  const [currentSheetIndex, setCurrentSheetIndex] = useState(0)
  const [selectedPiece, setSelectedPiece] = useState<any>(null)
  const [scale, setScale] = useState(1)

  useEffect(() => {
    if (optimizationResult?.layouts && optimizationResult.layouts.length > 0) {
      renderVisualization()
    }
  }, [optimizationResult, currentSheetIndex, scale])

  const renderVisualization = () => {
    if (!svgRef.current || !optimizationResult?.layouts) return

    const layout = optimizationResult.layouts[currentSheetIndex]
    if (!layout) return

    // Clear previous content
    d3.select(svgRef.current).selectAll('*').remove()

    const svg = d3.select(svgRef.current)
    const containerWidth = 800
    const containerHeight = 650
    const margin = { top: 20, right: 20, bottom: 60, left: 70 }

    // Calculate scale to fit the sheet in the container
    const sheetWidth = layout.widthUsed
    const sheetHeight = layout.heightUsed
    const availableWidth = containerWidth - margin.left - margin.right
    const availableHeight = containerHeight - margin.top - margin.bottom

    const scaleX = availableWidth / sheetWidth
    const scaleY = availableHeight / sheetHeight
    const autoScale = Math.min(scaleX, scaleY, 1) * scale

    // Create main group
    const g = svg
      .attr('width', containerWidth)
      .attr('height', containerHeight)
      .append('g')
      .attr('transform', `translate(${margin.left}, ${margin.top})`)

    // Add sheet background
    g.append('rect')
      .attr('x', 0)
      .attr('y', 0)
      .attr('width', sheetWidth * autoScale)
      .attr('height', sheetHeight * autoScale)
      .attr('fill', '#f8f9fa')
      .attr('stroke', '#dee2e6')
      .attr('stroke-width', 2)
      .attr('rx', 4)

    // Add pieces
    const pieces = g.selectAll('.piece')
      .data(layout.pieces)
      .enter()
      .append('g')
      .attr('class', 'piece')
      .attr('transform', (d: any) => `translate(${d.x * autoScale}, ${d.y * autoScale})`)

    // Add piece rectangles
    pieces.append('rect')
      .attr('width', (d: any) => d.packedWidth * autoScale)
      .attr('height', (d: any) => d.packedHeight * autoScale)
      .attr('fill', (d: any, i: number) => d.color || getPieceColor(i))
      .attr('stroke', '#ffffff')
      .attr('stroke-width', 1)
      .attr('rx', 2)
      .attr('class', 'piece-rect')
      .style('cursor', 'pointer')
      .style('transition', 'all 0.2s ease')
      .on('mouseover', function(event, d) {
        d3.select(this)
          .attr('stroke', '#3b82f6')
          .attr('stroke-width', 2)
          .style('filter', 'brightness(1.1)')
      })
      .on('mouseout', function(event, d: any) {
        if (selectedPiece?.id !== d.id) {
          d3.select(this)
            .attr('stroke', '#ffffff')
            .attr('stroke-width', 1)
            .style('filter', 'none')
        }
      })
      .on('click', function(event, d: any) {
        // Clear previous selection
        pieces.selectAll('rect')
          .attr('stroke', '#ffffff')
          .attr('stroke-width', 1)
          .style('filter', 'none')

        // Highlight selected piece
        d3.select(this)
          .attr('stroke', '#fbbf24')
          .attr('stroke-width', 3)
          .style('filter', 'brightness(1.1)')

        setSelectedPiece(d)
      })

    // Add piece labels
    pieces.append('text')
      .attr('x', (d: any) => (d.packedWidth * autoScale) / 2)
      .attr('y', (d: any) => (d.packedHeight * autoScale) / 2)
      .attr('text-anchor', 'middle')
      .attr('dominant-baseline', 'middle')
      .attr('class', 'piece-label')
      .style('font-size', `${Math.max(10, 12 * autoScale)}px`)
      .style('font-weight', '500')
      .style('fill', '#1f2937')
      .style('pointer-events', 'none')
      .text((d: any) => {
        const maxWidth = d.packedWidth * autoScale
        const maxChars = Math.floor(maxWidth / 8)
        return d.name.length > maxChars ? d.name.substring(0, maxChars - 3) + '...' : d.name
      })

    // Add dimension labels inside piece borders
    pieces.each(function(d: any) {
      const pieceWidth = d.packedWidth * autoScale
      const pieceHeight = d.packedHeight * autoScale
      const group = d3.select(this)

      // Only show dimension labels if pieces are large enough to be readable
      if (pieceWidth > 50 && pieceHeight > 40) {
        const fontSize = Math.max(9, 11 * autoScale)
        const labelOffset = 8 // Distance from piece border (inside)

        // Width dimension label (bottom edge, inside)
        group.append('text')
          .attr('x', pieceWidth / 2)
          .attr('y', pieceHeight - labelOffset)
          .attr('text-anchor', 'middle')
          .attr('dominant-baseline', 'baseline')
          .style('font-size', `${fontSize}px`)
          .style('font-family', 'Arial, sans-serif')
          .style('font-weight', '600')
          .style('fill', '#1f2937')
          .style('pointer-events', 'none')
          .style('text-shadow', '1px 1px 2px rgba(255,255,255,0.8)')
          .text(formatNumber(d.packedWidth))

        // Height dimension label (right edge, inside)
        group.append('text')
          .attr('x', pieceWidth - labelOffset)
          .attr('y', pieceHeight / 2)
          .attr('text-anchor', 'middle')
          .attr('dominant-baseline', 'middle')
          .attr('transform', `rotate(90, ${pieceWidth - labelOffset}, ${pieceHeight / 2})`)
          .style('font-size', `${fontSize}px`)
          .style('font-family', 'Arial, sans-serif')
          .style('font-weight', '600')
          .style('fill', '#1f2937')
          .style('pointer-events', 'none')
          .style('text-shadow', '1px 1px 2px rgba(255,255,255,0.8)')
          .text(formatNumber(d.packedHeight))
      }
    })

    // Add grid lines for better visualization
    const gridSpacing = 50 // mm
    const gridLines = g.append('g').attr('class', 'grid').style('opacity', 0.1)

    // Vertical grid lines
    for (let x = 0; x <= sheetWidth; x += gridSpacing) {
      gridLines.append('line')
        .attr('x1', x * autoScale)
        .attr('y1', 0)
        .attr('x2', x * autoScale)
        .attr('y2', sheetHeight * autoScale)
        .attr('stroke', '#6b7280')
        .attr('stroke-width', 0.5)
    }

    // Horizontal grid lines
    for (let y = 0; y <= sheetHeight; y += gridSpacing) {
      gridLines.append('line')
        .attr('x1', 0)
        .attr('y1', y * autoScale)
        .attr('x2', sheetWidth * autoScale)
        .attr('y2', y * autoScale)
        .attr('stroke', '#6b7280')
        .attr('stroke-width', 0.5)
    }

    // Add axis dimensions
    const axisGroup = g.append('g').attr('class', 'axis-dimensions')

    // X-axis dimensions (bottom)
    const xAxisSpacing = Math.max(100, Math.ceil(sheetWidth / 8 / 50) * 50) // Dynamic spacing based on sheet width
    for (let x = 0; x <= sheetWidth; x += xAxisSpacing) {
      // Tick marks
      axisGroup.append('line')
        .attr('x1', x * autoScale)
        .attr('y1', sheetHeight * autoScale)
        .attr('x2', x * autoScale)
        .attr('y2', sheetHeight * autoScale + 8)
        .attr('stroke', '#374151')
        .attr('stroke-width', 1)

      // Labels
      axisGroup.append('text')
        .attr('x', x * autoScale)
        .attr('y', sheetHeight * autoScale + 20)
        .attr('text-anchor', 'middle')
        .style('font-size', `${Math.max(10, 11 * autoScale)}px`)
        .style('font-family', 'Arial, sans-serif')
        .style('fill', '#374151')
        .text(formatNumber(x))
    }

    // Y-axis dimensions (left)
    const yAxisSpacing = Math.max(100, Math.ceil(sheetHeight / 6 / 50) * 50) // Dynamic spacing based on sheet height
    for (let y = 0; y <= sheetHeight; y += yAxisSpacing) {
      // Tick marks
      axisGroup.append('line')
        .attr('x1', -8)
        .attr('y1', y * autoScale)
        .attr('x2', 0)
        .attr('y2', y * autoScale)
        .attr('stroke', '#374151')
        .attr('stroke-width', 1)

      // Labels
      axisGroup.append('text')
        .attr('x', -12)
        .attr('y', y * autoScale)
        .attr('text-anchor', 'end')
        .attr('dominant-baseline', 'middle')
        .style('font-size', `${Math.max(10, 11 * autoScale)}px`)
        .style('font-family', 'Arial, sans-serif')
        .style('fill', '#374151')
        .text(formatNumber(sheetHeight - y)) // Invert Y to show from top-left origin

      // Add Y-axis line if it's the first tick
      if (y === 0) {
        axisGroup.append('line')
          .attr('x1', 0)
          .attr('y1', 0)
          .attr('x2', 0)
          .attr('y2', sheetHeight * autoScale)
          .attr('stroke', '#374151')
          .attr('stroke-width', 1)
      }
    }

    // Add X-axis line
    axisGroup.append('line')
      .attr('x1', 0)
      .attr('y1', sheetHeight * autoScale)
      .attr('x2', sheetWidth * autoScale)
      .attr('y2', sheetHeight * autoScale)
      .attr('stroke', '#374151')
      .attr('stroke-width', 1)

    // Add axis labels
    axisGroup.append('text')
      .attr('x', (sheetWidth * autoScale) / 2)
      .attr('y', sheetHeight * autoScale + 40)
      .attr('text-anchor', 'middle')
      .style('font-size', `${Math.max(12, 13 * autoScale)}px`)
      .style('font-family', 'Arial, sans-serif')
      .style('font-weight', '600')
      .style('fill', '#1f2937')
      .text(`Width (${layout.baseMaterial.unit})`)

    axisGroup.append('text')
      .attr('x', -50)
      .attr('y', (sheetHeight * autoScale) / 2)
      .attr('text-anchor', 'middle')
      .attr('transform', `rotate(-90, -50, ${(sheetHeight * autoScale) / 2})`)
      .style('font-size', `${Math.max(12, 13 * autoScale)}px`)
      .style('font-family', 'Arial, sans-serif')
      .style('font-weight', '600')
      .style('fill', '#1f2937')
      .text(`Height (${layout.baseMaterial.unit})`)

    // Add piece position indicators on axes
    const pieceAxisGroup = g.append('g').attr('class', 'piece-axis-indicators')

    layout.pieces.forEach((piece: any, index: number) => {
      const pieceColor = piece.color || getPieceColor(index)

      // X-axis indicators (bottom) - start and end positions
      const xStart = piece.x * autoScale
      const xEnd = (piece.x + piece.packedWidth) * autoScale

      // Start position indicator
      pieceAxisGroup.append('line')
        .attr('x1', xStart)
        .attr('y1', sheetHeight * autoScale + 2)
        .attr('x2', xStart)
        .attr('y2', sheetHeight * autoScale + 6)
        .attr('stroke', pieceColor)
        .attr('stroke-width', 2)

      // End position indicator
      pieceAxisGroup.append('line')
        .attr('x1', xEnd)
        .attr('y1', sheetHeight * autoScale + 2)
        .attr('x2', xEnd)
        .attr('y2', sheetHeight * autoScale + 6)
        .attr('stroke', pieceColor)
        .attr('stroke-width', 2)

      // Position labels on X-axis
      if (autoScale > 0.5) { // Only show labels when zoomed in enough
        pieceAxisGroup.append('text')
          .attr('x', xStart)
          .attr('y', sheetHeight * autoScale + 35)
          .attr('text-anchor', 'middle')
          .style('font-size', `${Math.max(8, 9 * autoScale)}px`)
          .style('font-family', 'Arial, sans-serif')
          .style('fill', pieceColor)
          .style('font-weight', '600')
          .text(formatNumber(piece.x))

        pieceAxisGroup.append('text')
          .attr('x', xEnd)
          .attr('y', sheetHeight * autoScale + 35)
          .attr('text-anchor', 'middle')
          .style('font-size', `${Math.max(8, 9 * autoScale)}px`)
          .style('font-family', 'Arial, sans-serif')
          .style('fill', pieceColor)
          .style('font-weight', '600')
          .text(formatNumber(piece.x + piece.packedWidth))
      }

      // Y-axis indicators (left) - start and end positions
      const yStart = piece.y * autoScale
      const yEnd = (piece.y + piece.packedHeight) * autoScale

      // Start position indicator
      pieceAxisGroup.append('line')
        .attr('x1', -6)
        .attr('y1', yStart)
        .attr('x2', -2)
        .attr('y2', yStart)
        .attr('stroke', pieceColor)
        .attr('stroke-width', 2)

      // End position indicator
      pieceAxisGroup.append('line')
        .attr('x1', -6)
        .attr('y1', yEnd)
        .attr('x2', -2)
        .attr('y2', yEnd)
        .attr('stroke', pieceColor)
        .attr('stroke-width', 2)

      // Position labels on Y-axis
      if (autoScale > 0.5) { // Only show labels when zoomed in enough
        pieceAxisGroup.append('text')
          .attr('x', -35)
          .attr('y', yStart)
          .attr('text-anchor', 'middle')
          .attr('dominant-baseline', 'middle')
          .style('font-size', `${Math.max(8, 9 * autoScale)}px`)
          .style('font-family', 'Arial, sans-serif')
          .style('fill', pieceColor)
          .style('font-weight', '600')
          .text(formatNumber(sheetHeight - piece.y)) // Invert Y for display

        pieceAxisGroup.append('text')
          .attr('x', -35)
          .attr('y', yEnd)
          .attr('text-anchor', 'middle')
          .attr('dominant-baseline', 'middle')
          .style('font-size', `${Math.max(8, 9 * autoScale)}px`)
          .style('font-family', 'Arial, sans-serif')
          .style('fill', pieceColor)
          .style('font-weight', '600')
          .text(formatNumber(sheetHeight - (piece.y + piece.packedHeight))) // Invert Y for display
      }
    })
  }

  const handlePreviousSheet = () => {
    setCurrentSheetIndex(prev => Math.max(0, prev - 1))
    setSelectedPiece(null)
  }

  const handleNextSheet = () => {
    setCurrentSheetIndex(prev => 
      Math.min(optimizationResult.layouts.length - 1, prev + 1)
    )
    setSelectedPiece(null)
  }

  const handleZoomIn = () => {
    setScale(prev => Math.min(prev * 1.2, 3))
  }

  const handleZoomOut = () => {
    setScale(prev => Math.max(prev / 1.2, 0.3))
  }

  const handleResetZoom = () => {
    setScale(1)
  }

  if (!optimizationResult?.layouts || optimizationResult.layouts.length === 0) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-16">
          <Eye className="h-16 w-16 text-gray-400 mb-4" />
          <h3 className="text-xl font-semibold text-gray-900 mb-2">No visualization available</h3>
          <p className="text-gray-600 text-center mb-6 max-w-md">
            Run the optimization algorithm to generate cutting layouts and view the interactive visualization.
          </p>
          <Button onClick={onOptimize} disabled={optimizing} className="bg-green-600 hover:bg-green-700">
            {optimizing ? (
              'Optimizing...'
            ) : (
              <>
                <Zap className="h-4 w-4 mr-2" />
                Run Optimization
              </>
            )}
          </Button>
        </CardContent>
      </Card>
    )
  }

  const currentLayout = optimizationResult.layouts[currentSheetIndex]

  return (
    <div className="space-y-6">
      {/* Uncut Pieces Warning */}
      {optimizationResult.uncutPieces && optimizationResult.uncutPieces.length > 0 && (
        <Alert variant="destructive">
          <Package className="h-4 w-4" />
          <AlertDescription>
            <strong>Warning:</strong> {optimizationResult.uncutPieces.reduce((sum: number, piece: any) => sum + piece.quantity, 0)} piece(s) could not be cut with the available materials. Check the "Cutting List" tab for details.
          </AlertDescription>
        </Alert>
      )}

      {/* Controls */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Interactive Visualization</CardTitle>
              <CardDescription>
                Click on pieces to see details • Use controls to navigate and zoom
              </CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" onClick={handleZoomOut}>
                -
              </Button>
              <Button variant="outline" size="sm" onClick={handleResetZoom}>
                <RotateCcw className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="sm" onClick={handleZoomIn}>
                +
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="outline"
                size="sm"
                onClick={handlePreviousSheet}
                disabled={currentSheetIndex === 0}
              >
                <ChevronLeft className="h-4 w-4 mr-1" />
                Previous
              </Button>
              <span className="text-sm font-medium">
                Sheet {currentSheetIndex + 1} of {optimizationResult.layouts.length}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={handleNextSheet}
                disabled={currentSheetIndex === optimizationResult.layouts.length - 1}
              >
                Next
                <ChevronRight className="h-4 w-4 ml-1" />
              </Button>
            </div>
            <div className="text-sm text-gray-600">
              {currentLayout.baseMaterial.name} • 
              {formatNumber(currentLayout.widthUsed)} × {formatNumber(currentLayout.heightUsed)} {currentLayout.baseMaterial.unit} • 
              {currentLayout.efficiency.toFixed(1)}% efficiency
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Visualization */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        <div className="lg:col-span-3">
          <Card>
            <CardContent className="p-4">
              <div className="visualization-container">
                <svg ref={svgRef} className="w-full border rounded"></svg>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Piece Info Panel */}
        <div className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Sheet Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <div className="text-sm text-gray-600">Material</div>
                <div className="font-medium">{currentLayout.baseMaterial.name}</div>
              </div>
              <div>
                <div className="text-sm text-gray-600">Dimensions</div>
                <div className="font-medium">
                  {formatNumber(currentLayout.widthUsed)} × {formatNumber(currentLayout.heightUsed)} {currentLayout.baseMaterial.unit}
                </div>
              </div>
              <div>
                <div className="text-sm text-gray-600">Pieces</div>
                <div className="font-medium">{currentLayout.pieces.length}</div>
              </div>
              <div>
                <div className="text-sm text-gray-600">Efficiency</div>
                <div className="font-medium text-green-600">{currentLayout.efficiency.toFixed(1)}%</div>
              </div>
              <div>
                <div className="text-sm text-gray-600">Waste Area</div>
                <div className="font-medium text-orange-600">
                  {formatNumber(currentLayout.wasteArea)} {currentLayout.baseMaterial.unit}²
                </div>
              </div>
            </CardContent>
          </Card>

          {selectedPiece && (
            <Card className="piece-info-panel">
              <CardHeader>
                <CardTitle className="text-lg">Selected Piece</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <div className="text-sm text-gray-600">Name</div>
                  <div className="font-medium">{selectedPiece.name}</div>
                </div>
                <div>
                  <div className="text-sm text-gray-600">Dimensions</div>
                  <div className="font-medium">
                    {formatNumber(selectedPiece.packedWidth)} × {formatNumber(selectedPiece.packedHeight)} {selectedPiece.unit}
                  </div>
                </div>
                <div>
                  <div className="text-sm text-gray-600">Position</div>
                  <div className="font-medium">
                    ({formatNumber(selectedPiece.x)}, {formatNumber(selectedPiece.y)})
                  </div>
                </div>
                {selectedPiece.rotation > 0 && (
                  <div>
                    <div className="text-sm text-gray-600">Rotation</div>
                    <div className="font-medium text-blue-600">{selectedPiece.rotation}°</div>
                  </div>
                )}
                <div>
                  <div className="text-sm text-gray-600">Area</div>
                  <div className="font-medium">
                    {formatNumber(selectedPiece.packedWidth * selectedPiece.packedHeight)} {selectedPiece.unit}²
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {!selectedPiece && (
            <Alert>
              <Eye className="h-4 w-4" />
              <AlertDescription>
                Click on any piece in the visualization to see detailed information.
              </AlertDescription>
            </Alert>
          )}
        </div>
      </div>
    </div>
  )
}
