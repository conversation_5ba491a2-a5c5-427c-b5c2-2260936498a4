# Test Plan: Optimization Result Persistence

## Objective
Verify that optimization results are saved to the database and restored when a user reopens a project.

## Test Steps

### 1. Setup Test Data
- Create a test project with some pieces
- Add some materials to inventory
- Ensure database is accessible

### 2. Test Optimization Saving
1. Navigate to a project
2. Run optimization
3. Verify optimization result is displayed
4. Check database to confirm optimization result was saved

### 3. Test Optimization Restoration
1. Navigate away from the project (go to projects list)
2. Navigate back to the same project
3. Verify that the previous optimization result is displayed
4. Verify that the visualization shows the saved layout

### 4. Test Multiple Optimizations
1. Run optimization again on the same project
2. Verify new result replaces the old one
3. Navigate away and back
4. Verify only the latest optimization is shown

## Expected Behavior

### Database Structure
- `optimization_results` table should contain the optimization metadata
- `sheets` table should contain sheet information for each layout
- `placed_pieces` table should contain piece placement data
- `optimization_materials` table should contain materials used

### UI Behavior
- When opening a project with saved optimization, visualization should show immediately
- All interactive features (piece selection, sheet navigation) should work
- Cutting list should display correctly
- Print functionality should work with saved results

## Manual Testing Checklist

- [ ] Create project with pieces
- [ ] Add materials to inventory
- [ ] Run optimization
- [ ] Verify result displays correctly
- [ ] Navigate away from project
- [ ] Navigate back to project
- [ ] Verify optimization result is restored
- [ ] Test piece selection in visualization
- [ ] Test sheet navigation
- [ ] Test print functionality
- [ ] Run new optimization
- [ ] Verify new result replaces old one
- [ ] Test persistence of new result

## Database Verification Queries

```sql
-- Check optimization results
SELECT * FROM optimization_results WHERE project_id = 'PROJECT_ID';

-- Check sheets
SELECT * FROM sheets WHERE optimization_result_id = 'OPTIMIZATION_ID';

-- Check placed pieces
SELECT * FROM placed_pieces WHERE sheet_id = 'SHEET_ID';

-- Check optimization materials
SELECT * FROM optimization_materials WHERE optimization_result_id = 'OPTIMIZATION_ID';
```

## Implementation Status

✅ Database schema exists
✅ Optimization API saves results to database
✅ Project detail page loads latest optimization result
✅ Transformation function converts DB format to API format
⏳ Manual testing required
⏳ Edge case testing required
